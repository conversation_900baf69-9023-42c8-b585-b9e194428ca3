﻿using System;
using System.Collections.Generic;
using System.Text;

namespace VOL.Core.Const
{
    public struct HtmlElementType
    {
        public const string drop = "drop";
        public const string droplist = "droplist";
        public const string select = "select";
        public const string selectlist = "selectlist";
        public const string checkbox = "checkbox";
        public const string textarea = "textarea";
        public const string thanorequal = "thanorequal";
        public const string lessorequal = "lessorequal"; 


        public const string gt = "gt";
        public const string lt = "lt";
        public const string GT = ">";
        public const string LT = "<";
        public const string like = "like";

        public const string ThanOrEqual = ">=";
        public const string LessOrequal = "<=";
        public const string Contains = "in";
        public const string Equal = "=";
    }
}
