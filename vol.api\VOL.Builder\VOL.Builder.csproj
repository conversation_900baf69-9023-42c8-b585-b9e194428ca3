﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName></SccProjectName>
    <SccProvider></SccProvider>
    <SccAuxPath></SccAuxPath>
    <SccLocalPath></SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ApplicationIcon />
    <OutputType>Library</OutputType>
    <StartupObject />
    <AssemblyName>VolPro.Builder</AssemblyName>
    <RootNamespace>VolPro.Builder</RootNamespace>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;NU1902;NU1903;NU1904;</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702;NU1902;NU1903;NU1904;</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Autofac" Version="8.0.0" />
    <PackageReference Include="AutoMapper" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\VOL.Core\VOL.Core.csproj" />
    <ProjectReference Include="..\VOL.Entity\VOL.Entity.csproj" />
  </ItemGroup>

</Project>
